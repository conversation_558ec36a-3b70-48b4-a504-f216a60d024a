from unittest.mock import MagicMock, patch
from uuid import uuid4, UUID

import pendulum
import pytest

from app.graphql_api import schema as graphql_schema
from ciba_participant.classes.models import TopicEnum
from tests.integration.graphql_api.classes.scenarios_test import (
    scenario_1,  # noqa: F401
    scenario_2,  # noqa: F401
    scenario_3,  # noqa: F401
    scenario_4,  # noqa: F401
    scenario_5,  # noqa: F401
    scenario_6,  # noqa: F401
    scenario_7,  # noqa: F401
    scenario_8,  # noqa: F401
)


class Request:
    headers: dict


def build_mock_response(topic: TopicEnum):
    mock_host = MagicMock(
        id=UUID("12345678-1234-5678-1234-************"),
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
        chat_identity="john_doe",
    )
    mock_host.full_name.return_value = "<PERSON>"

    mock_webinar = MagicMock(
        topic=topic,
        host=mock_host,
        max_capacity=10,
    )

    mock_upcoming_class = MagicMock(
        id=UUID("*************-8765-4321-************"),
        title="Test Class",
        description="Test Description",
        meeting_start_time=pendulum.now(),
        bookings=[],
        zoom_link="https://zoom.us/test",
        recording_url="https://zoom.us/recording/test",
        webinar=mock_webinar,
    )

    return mock_upcoming_class


@pytest.fixture
def mock_class_response():
    return build_mock_response(topic=TopicEnum.EDUCATIONAL)


@pytest.fixture
def mock_intro_response():
    return build_mock_response(topic=TopicEnum.INTRO_SESSION)


UPCOMING_CLASSES_QUERY = """
        query ExampleQuery($participantId: UUID!) {
          getUpcomingClasses(participantId: $participantId) {
            items {
              id,
              title,
              description,
              topic,
              status,
              startedAt
            }
          }
        }
"""

BOOKINGS_QUERY = """
        query ExampleQuery($participantId: UUID!) {
          getBookingsByParticipant(participantId: $participantId) {
            items {
              id,
              title,
              description,
              topic,
              status,
              startedAt
            }
          }
        }
"""

BOOKINGS_QUERY_2 = """
        query ExampleQuery($participantId: UUID!, $filters: BookingFilter, $page: Int!, $perPage: Int!) {
          getBookingsByParticipant(participantId: $participantId, filters: $filters, page:$page, perPage:$perPage) {
            items {
              id,
              title,
              description,
              topic,
              status,
              startedAt
            }
            pageInfo{
              hasNextPage,
              hasPreviousPage,
              currentPage,
              perPage,
              lastPage,
              total,
            }
          }
        }
"""


def init_context():
    variables = {"participantId": str(uuid4())}
    request = Request()
    request.headers = {"Time-Zone-Offset": "00:00"}
    mock_context = type(
        "Context",
        (),
        {
            "user": type(
                "User", (), {"is_authenticated": True, "sub": "123"}
            )(),
            "request": request,
        },
    )()

    return variables, mock_context


@pytest.mark.asyncio
async def test_get_upcoming_has_seen_intro(mock_class_response, mocker):
    """
    get_upcoming_classes should return a list of all upcoming classes.
    """
    variables, mock_context = init_context()
    mock_page = MagicMock()
    mock_page.items = [mock_class_response, mock_class_response]

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
        mocker.patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_requested_page",
            return_value=mock_page,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values=variables,
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]
        assert actual_value.data["getUpcomingClasses"] is not None
        assert len(class_list) == 2

        for c in class_list:
            assert c["topic"] == "EDUCATIONAL"


@pytest.mark.asyncio
async def test_get_upcoming_has_not_seen_intro(mock_intro_response, mocker):
    """
    case when the participant has not seen an intro session
    """
    variables, mock_context = init_context()
    mock_page_1 = MagicMock()
    mock_page_1.items = []
    mock_page_2 = MagicMock()
    mock_page_2.items = [mock_intro_response]

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
        mocker.patch(
            "app.graphql_api.classes.queries.upcoming_classes.get_requested_page",
            side_effect=[mock_page_1, mock_page_2],
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values=variables,
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]
        assert class_list is not None
        assert len(class_list) == 1
        assert class_list[0]["topic"] == "INTRO_SESSION"


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_has_booked(scenario_1, mocker):  # noqa: F811
    """
    case when the participant has booked an intro session on enrollment
    """
    _, mock_context = init_context()
    data = await scenario_1.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        upcoming_result = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert upcoming_result.errors is None
        class_list = upcoming_result.data["getUpcomingClasses"]["items"]

        # Should come zero classes
        assert class_list is not None
        assert len(class_list) == 0

        bookings_result = await graphql_schema.execute(
            BOOKINGS_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert bookings_result.errors is None

        class_list = bookings_result.data["getBookingsByParticipant"]["items"]
        session = class_list[0]

        assert session["topic"] == "INTRO_SESSION"
        assert session["status"] == "BOOKED"
        assert session["startedAt"] == str(
            data["live_session"].meeting_start_time
        ).replace(" ", "T")


def get_one_hour_ago():
    return str(pendulum.now().subtract(hours=1))


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.bookings_by_participant.get_one_hour_ago",
    get_one_hour_ago,
)  # SQLite test DB does not support DateTime objects
async def test_multiple_booking_statuses(scenario_7, mocker):  # noqa: F811
    """
    case when the participant has booked an intro session on enrollment
    """
    _, mock_context = init_context()
    data = await scenario_7.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        bookings_result = await graphql_schema.execute(
            BOOKINGS_QUERY_2,
            variable_values={
                "participantId": str(data["participant"].id),
                "filters": {
                    "bookingStatus": [
                        "BOOKED",
                        "WATCHED_RECORDING",
                    ],
                },
                "page": 1,
                "perPage": 12,
            },
            context_value=mock_context,
        )

        assert bookings_result.errors is None

        class_list = bookings_result.data["getBookingsByParticipant"]["items"]
        assert len(class_list) == 2
        session_1 = class_list[0]

        assert session_1["topic"] == "INTRO_SESSION"
        assert session_1["status"] == "PAST"
        assert session_1["startedAt"] == str(
            data["live_session_1"].meeting_start_time
        ).replace(" ", "T")

        session_2 = class_list[1]

        assert session_2["topic"] == "MENTAL_HEALTH"
        assert session_2["status"] == "BOOKED"
        assert session_2["startedAt"] == str(
            data["live_session_2"].meeting_start_time
        ).replace(" ", "T")


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_has_not_booked(scenario_2, mocker):  # noqa: F811
    """
    user is enrolled and does not have an intro session booking
    """
    _, mock_context = init_context()
    data = await scenario_2.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]
        assert class_list is not None
        assert len(class_list) == 1

        session = class_list[0]

        assert session["topic"] == "INTRO_SESSION"
        assert session["status"] == "AVAILABLE_TO_BOOK"


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_legacy_session(scenario_3, mocker):  # noqa: F811
    """
    case when the participant was enrolled before classes. Should
    not see intro classes.
    """
    _, mock_context = init_context()
    data = await scenario_3.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]
        session = class_list[0]

        assert class_list is not None
        assert len(class_list) == 1
        assert session["topic"] == "MENTAL_HEALTH"
        assert session["status"] == "AVAILABLE_TO_BOOK"


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_after_30_day_limit(scenario_4, mocker):  # noqa: F811
    """
    case when the next class is more than 30 days away.
    """
    _, mock_context = init_context()
    data = await scenario_4.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]

        assert class_list is not None
        assert len(class_list) == 0


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_7_day_limit(scenario_5, mocker):  # noqa: F811
    """
    case when it shows intro sessions that are max 7 days after cohort start.
    """
    _, mock_context = init_context()
    data = await scenario_5.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]

        assert class_list is not None
        assert len(class_list) == 2
        assert class_list[0]["id"] == str(data["live_session_1"].id)
        assert class_list[1]["id"] == str(data["live_session_2"].id)


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_after_7_day_limit(scenario_6, mocker):  # noqa: F811
    """
    case when the participant is more than a week after cohort start.
    Should show all upcoming sessions for the next 30 days.
    """
    _, mock_context = init_context()
    data = await scenario_6.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]

        assert class_list is not None
        assert len(class_list) == 3

        class_ids = list(map(lambda c: str(c["id"]), class_list))

        assert str(data["live_session_1"].id) in class_ids
        assert str(data["live_session_2"].id) in class_ids
        assert str(data["live_session_3"].id) in class_ids


@pytest.mark.asyncio
@patch(
    "app.graphql_api.classes.queries.upcoming_classes.get_dates_query_filter",
    lambda s, e: {
        "meeting_start_time__gte": str(s),
        "meeting_start_time__lte": str(e),
    },
)  # SQLite test DB does not support DateTime objects
async def test_no_intros_before_cohort_start(scenario_8, mocker):  # noqa: F811
    """
    case when there are intro sessions available before cohort start, which shouldn't be visible to the participant.
    """
    _, mock_context = init_context()
    data = await scenario_8.__anext__()

    with (
        mocker.patch(
            "app.auth.permissions.IsAuthenticated.has_permission",
            return_value=True,
        ),
    ):
        actual_value = await graphql_schema.execute(
            UPCOMING_CLASSES_QUERY,
            variable_values={"participantId": str(data["participant"].id)},
            context_value=mock_context,
        )

        assert actual_value.errors is None

        class_list = actual_value.data["getUpcomingClasses"]["items"]

        assert class_list is not None
        assert len(class_list) == 2

        class_ids = list(map(lambda c: str(c["id"]), class_list))

        assert str(data["live_session_1"].id) in class_ids
        assert str(data["live_session_4"].id) in class_ids
