Metadata-Version: 2.4
Name: participant-api
Version: 3.2.0
Requires-Python: >=3.12
Description-Content-Type: text/markdown
Requires-Dist: asgi-correlation-id>=4.3.4
Requires-Dist: asgiref>=3.5.2
Requires-Dist: aws-encryption-sdk>=3.1.1
Requires-Dist: boto3>=1.26.10
Requires-Dist: celery>=5.2.7
Requires-Dist: ciba-participant
Requires-Dist: cryptography>=43.0.1
Requires-Dist: fastapi[standard]>=0.115.6
Requires-Dist: libcst>=1.4.0
Requires-Dist: loguru>=0.7.2
Requires-Dist: pendulum>=3.1.0
Requires-Dist: psycopg>=3.1.19
Requires-Dist: psycopg-binary>=3.2.2
Requires-Dist: psycopg-pool>=3.2.2
Requires-Dist: psycopg2-binary>=2.9.9
Requires-Dist: pycryptodome>=3.20.0
Requires-Dist: pydantic-settings>=2.3.4
Requires-Dist: pyjwt>=2.9.0
Requires-Dist: python-dotenv>=1.0.1
Requires-Dist: python-jose>=3.4.0
Requires-Dist: python-multipart>=0.0.18
Requires-Dist: python-redis-cache>=1.2.0
Requires-Dist: python-retry>=0.0.1
Requires-Dist: pytz>=2023.3.post1
Requires-Dist: redis>=6.2.0
Requires-Dist: requests>=2.32.3
Requires-Dist: respx>=0.21.1
Requires-Dist: ruff>=0.1.3
Requires-Dist: sendgrid>=6.9.7
Requires-Dist: sentry-sdk>=1.45.1
Requires-Dist: stackprinter>=0.2.9
Requires-Dist: strawberry-graphql>=0.243.0
Requires-Dist: tortoise-orm>=0.21.6
Requires-Dist: types-pytz>=2023.3.1.1
Requires-Dist: typing-extensions>=4.12.2

## Participant Api

### Env setup

### MacOS

#### Python

follow [this](https://medium.com/marvelous-mlops/the-rightway-to-install-python-on-a-mac-f3146d9d9a32) guide

#### UV

follow [this](https://docs.astral.sh/uv/)

#### Docker

follow [this](https://docs.docker.com/desktop/install/mac-install/)

#### Setup your github ssh-key

follow [this](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent)

### Running locally

#### Build participant-api dev image

```shell
make build
```

#### Run docker-compose

```shell
make up
```

#### Run terminal

```shell
make term
```

P.S works on OS X

P.P.S you have to have your github ssh-key added to be able to install packages from private repositories and share these keys with docker containers

#### Debug app in built image

##### VSCode Web

http://localhost:3005

Access password is available in [entrypoint.sh](entrypoint.sh)

##### JetBrains Gateway

https://www.jetbrains.com/remote-development/gateway/

##### Unit Tests

Run unit tests with command:

```shell
uv run pytest
```
