import asyncio
from datetime import datetime, timezone
from typing import Optional
from fastapi import APIRouter, HTTPException, Security
from fastapi.security import API<PERSON>eyHeader
from pydantic import BaseModel, EmailStr, validator, Field
from ciba_participant.activity.models import ParticipantActivityDevice
from ciba_participant.cohort.models import <PERSON><PERSON><PERSON>
from ciba_participant.participant.models import Participant, ParticipantMeta
from ciba_participant.rpm_api.api import get_single_device_status
from ciba_participant.rpm_api.models import (
    DetailedConnectionStatus,
    DeviceStatusEnum,
    DeviceTypeEnum,
)
from app.log.logging import logger
from app.auth.constants import X_AUTH_KEY
from app.models.devices import ConnectionStatus
from app.services.devices.utils import get_participant_from_email
from app.services.devices.handler import RPMRequestHandler
import pendulum
from http import HTTPStatus

# Constants
PARTICIPANT = "participant"
USER_NOT_FOUND = "User not found"
DEVICE_DISCONNECTED = "Device disconnected"
DEVICE_CONNECTED = "Device connected"
ERROR_ON_DISCONNECTING = "Error on disconnecting"

devices = APIRouter(
    prefix="/devices",
    dependencies=[
        Security(APIKeyHeader(name=X_AUTH_KEY)),
    ],
)


class RPMRequest(BaseModel):
    """Request model for RPM operations with input validation"""
    type_device: ParticipantActivityDevice
    email: EmailStr

    @validator('email')
    def validate_email(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Email cannot be empty')
        return v.lower().strip()

    @validator('type_device')
    def validate_device_type(cls, v):
        if v not in ParticipantActivityDevice:
            raise ValueError(f'Invalid device type: {v}')
        return v


async def set_user_weight_status(
    participant: Participant, is_weight: bool = False, correlation_id: Optional[str] = None
) -> bool:
    """
    Set the weight status for a participant in their metadata.

    Args:
        participant: The participant to update
        is_weight: Whether the participant has weight tracking enabled
        correlation_id: Optional correlation ID for debugging

    Returns:
        bool: True if successful, False otherwise
    """
    if correlation_id is None:
        correlation_id = str(pendulum.now().int_timestamp)

    try:
        participant_meta = await ParticipantMeta.get_or_none(participant=participant)

        if participant_meta is None:
            logger.warning(
                f"[Correlation ID: {correlation_id}] No metadata found for participant {participant.id}, creating new record"
            )
            # Create new metadata record if none exists
            metadata = {"is_weight": is_weight}
            participant_meta = await ParticipantMeta.create(
                participant=participant,
                metadata=metadata
            )
            logger.debug(
                f"[Correlation ID: {correlation_id}] Created new metadata for participant: {participant.email}"
            )
            return True

        # Get existing metadata or initialize empty dict
        metadata = participant_meta.metadata or {}
        db_is_weight = metadata.get("is_weight", False)

        logger.debug(
            f"[Correlation ID: {correlation_id}] Current weight status: {db_is_weight}, "
            f"setting to: {is_weight} for participant: {participant.email}"
        )

        # Update metadata
        metadata["is_weight"] = is_weight

        # Update the record
        rows_updated = await ParticipantMeta.filter(
            id=participant_meta.id
        ).update(metadata=metadata)

        # Verify the update was successful
        if rows_updated > 0:
            logger.debug(
                f"[Correlation ID: {correlation_id}] Successfully updated metadata for participant: {participant.email}"
            )
            return True
        else:
            logger.warning(
                f"[Correlation ID: {correlation_id}] No rows updated when setting weight status for participant: {participant.email}"
            )
            return False

    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Exception occurred while setting user weight status: {e}",
            exc_info=True
        )
        logger.debug(f"[Correlation ID: {correlation_id}] Participant: {participant.id}")
        logger.debug(f"[Correlation ID: {correlation_id}] Attempted is_weight value: {is_weight}")
        return False


@devices.get("/get_code")
async def get_code(
    type_device: ParticipantActivityDevice, mail: str, site: str | None = None
) -> dict:
    try:
        correlation_id = str(pendulum.now().int_timestamp)
        participant = await get_participant_from_email(mail)

        response: DetailedConnectionStatus = await get_single_device_status(
            participant_id=participant.id, device_type=type_device
        )

        healthy = response.healthy
        token = response.token
        status = response.status

        logger.debug(
            f"[Correlation ID: {correlation_id}] Device status - "
            f"healthy: {healthy}, token: {'present' if token else 'none'}, "
            f"status: {status}, device: {type_device}"
        )

        # Scenario 1: Healthy device with valid token (WITHINGS only)
        if (
            healthy is True
            and token is not None
            and type_device == ParticipantActivityDevice.WITHINGS
        ):
            logger.debug(f"[Correlation ID: {correlation_id}] Healthy WITHINGS device, setting weight status")
            await set_user_weight_status(participant, True, correlation_id)

            # Safely remove auth_url if it exists
            if hasattr(response, 'auth_url'):
                delattr(response, 'auth_url')

        # Scenario 2: Unhealthy device that needs reconnection/new auth
        elif not healthy and status in [
            DeviceStatusEnum.NOT_CONNECTED,
            DeviceStatusEnum.RECONNECT,
        ]:
            logger.debug(f"[Correlation ID: {correlation_id}] Device needs reconnection, getting auth URL")

            await participant.fetch_related("cohort")
            cohorts = await Cohort.filter(participants__id=participant.id)
            cohorts_num = len(cohorts)

            sync_start_date = (
                min(cohort.started_at.timestamp() for cohort in cohorts)
                if cohorts_num >= 1
                else datetime.now(timezone.utc).timestamp()
            )

            auth_response = await RPMRequestHandler().get_device_auth(
                participant,
                type_device,
                mail,
                sync_start_date,
                site,
                correlation_id,
            )

            if "auth_url" in auth_response:
                response.auth_url = auth_response["auth_url"]

            # Safely remove sensitive fields
            for field in ['token', 'account_id', 'subscription']:
                if hasattr(response, field):
                    delattr(response, field)

        # Scenario 3: Unhealthy device but has valid credentials
        # This handles cases where device is authenticated but has operational issues
        elif not healthy and token is not None:
            logger.debug(
                f"[Correlation ID: {correlation_id}] Device unhealthy but has valid token, "
                f"returning status as-is for client handling"
            )
            # Return the response as-is, let the client handle the unhealthy status
            # This covers your specific case: healthy=false but token/account_id/subscription exist

            # Safely remove auth_url if it exists (not needed when device has token)
            if hasattr(response, 'auth_url'):
                delattr(response, 'auth_url')

        # Scenario 4: Device is unhealthy and has no valid credentials
        else:
            logger.error(
                f"[Correlation ID: {correlation_id}] Device in unhandleable state - "
                f"healthy: {healthy}, token: {'present' if token else 'none'}, status: {status}"
            )
            raise HTTPException(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                detail={
                    "issue": f"{type_device.name} connection is not healthy and has no valid credentials",
                    "recommendation": "Please reconnect your device or contact support.",
                    "status": status.value if status else "unknown",
                    "healthy": healthy
                },
            )

        logger.debug(f"[Correlation ID: {correlation_id}] Returning response for {type_device}")
        return response.__dict__

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Unexpected error in get_code: {e}",
            exc_info=True
        )
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error when getting device status: {e}",
        ) from e


@devices.get("/get_codes")
async def get_codes(mail: str, site: str | None = None) -> list:
    """
    Route to get the auth_url from request to rpm

    Args:
        mail: Participant email address
        site: Optional site parameter

    Returns:
        list: List of device information with auth URLs
    """
    correlation_id = str(pendulum.now().int_timestamp)

    try:
        # Validate participant exists
        participant = await get_participant_from_email(mail)

        logger.debug(f"[Correlation ID: {correlation_id}] Getting device codes for participant: {participant.email}, site: {site}")

        devices_return = []

        # Process devices concurrently for better performance
        async def get_device_code(device):
            one_device = {"name": device.value}

            try:
                if site is None:
                    endpoint = "?mail={mail}&type_device={type_device}"
                    prepared_data = {"type_device": device.value, "mail": mail}
                else:
                    endpoint = "?mail={mail}&type_device={type_device}&site={site}"
                    prepared_data = {
                        "type_device": device.value,
                        "mail": mail,
                        "site": site,
                    }

                logger.debug(f"[Correlation ID: {correlation_id}] Fetching code for {device.value}")
                response = await RPMRequestHandler(endpoint).get_data(prepared_data)

                if response and isinstance(response, dict):
                    one_device.update(response)
                    logger.debug(f"[Correlation ID: {correlation_id}] Successfully updated {device.value} code")
                else:
                    logger.warning(f"[Correlation ID: {correlation_id}] Invalid response for {device.value}: {response}")
                    one_device.update({"error": "Invalid response from device service"})

            except Exception as e:
                logger.error(
                    f"[Correlation ID: {correlation_id}] Error fetching code for {device.value}: {e}",
                    exc_info=True
                )
                one_device.update({"error": f"Failed to fetch device code: {str(e)}"})

            return one_device

        # Execute all device code requests concurrently
        devices_return = await asyncio.gather(*[get_device_code(device) for device in ParticipantActivityDevice])

        logger.debug(f"[Correlation ID: {correlation_id}] Successfully processed all device codes")
        return devices_return

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Unexpected error in get_codes: {e}",
            exc_info=True
        )
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error when getting device codes: {e}",
        ) from e


@devices.get("/status")
async def get_connection_status(
    type_device: DeviceTypeEnum, mail: str
) -> ConnectionStatus:
    """
    Route to get the provided device connection status.

    Args:
        type_device: The type of device to check status for
        mail: Participant email address

    Returns:
        ConnectionStatus: The connection status information
    """
    correlation_id = str(pendulum.now().int_timestamp)

    try:
        participant = await get_participant_from_email(mail)

        logger.debug(f"[Correlation ID: {correlation_id}] Getting connection status for {type_device} for participant: {participant.email}")

        response = await get_single_device_status(
            participant_id=str(participant.id), device_type=type_device
        )

        if response is None:
            logger.warning(f"[Correlation ID: {correlation_id}] No response from get_single_device_status")
            raise HTTPException(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                detail="Unable to retrieve device status"
            )

        logger.debug(f"[Correlation ID: {correlation_id}] Successfully retrieved connection status")
        return ConnectionStatus(
            device=response.device,
            status=response.status,
            healthy=response.healthy,
            account_id=response.account_id,
        )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Unexpected error in get_connection_status: {e}",
            exc_info=True
        )
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error when getting connection status: {e}",
        ) from e


@devices.get("/add_user")
async def add_user(mail: str) -> dict[str, str]:
    """
    Route to get the auth_url from request to rpm

    Args:
        mail: Participant email address

    Returns:
        dict: Response from RPM service
    """
    correlation_id = str(pendulum.now().int_timestamp)

    try:
        logger.debug(f"[Correlation ID: {correlation_id}] Adding user with email: {mail}")

        # Use the standardized participant lookup function
        participant = await get_participant_from_email(mail)

        # Fix the malformed endpoint URL (remove duplicate ?mail=)
        endpoint = f"i-devices/add?mail={participant.email}"
        prepared_data = {"mail": participant.email}

        logger.debug(f"[Correlation ID: {correlation_id}] Calling RPM service to add user")
        response = await RPMRequestHandler(endpoint).post_data(prepared_data)

        if response is None:
            logger.warning(f"[Correlation ID: {correlation_id}] No response from RPM service")
            raise HTTPException(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                detail="Unable to add user to device service"
            )

        logger.debug(f"[Correlation ID: {correlation_id}] Successfully added user")
        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Unexpected error in add_user: {e}",
            exc_info=True
        )
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error when adding user: {e}",
        ) from e


@devices.post("/sync")
async def sync(request: RPMRequest) -> dict[str, str]:
    """
    Route to sync request to rpm

    Args:
        request: RPM request containing email and device type

    Returns:
        dict: Response from RPM sync service
    """
    correlation_id = str(pendulum.now().int_timestamp)

    try:
        logger.debug(f"[Correlation ID: {correlation_id}] Syncing device {request.type_device} for email: {request.email}")

        # Use the standardized participant lookup function
        participant = await get_participant_from_email(request.email)

        logger.debug(f"[Correlation ID: {correlation_id}] Calling RPM sync service")
        response = await RPMRequestHandler().sync(
            type_device=request.type_device.value,
            member_type=PARTICIPANT,
            member_id=str(participant.id),
        )

        if response is None:
            logger.warning(f"[Correlation ID: {correlation_id}] No response from RPM sync service")
            raise HTTPException(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                detail="Unable to sync with device service"
            )

        logger.debug(f"[Correlation ID: {correlation_id}] Successfully synced device")
        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Unexpected error in sync: {e}",
            exc_info=True
        )
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"Error when syncing device: {e}",
        ) from e


@devices.post("/disconnect")
async def disconnect(request: RPMRequest) -> dict[str, str]:
    """
    Route to disconnect request to rpm

    Args:
        request: RPM request containing email and device type

    Returns:
        dict: Response from RPM disconnect service
    """
    correlation_id = str(pendulum.now().int_timestamp)

    try:
        logger.info(
            f"[Correlation ID: {correlation_id}] Disconnecting user: {request.email} from {request.type_device}"
        )

        # Use the standardized participant lookup function
        participant = await get_participant_from_email(request.email)

        logger.debug(f"[Correlation ID: {correlation_id}] Calling RPM disconnect service")
        response = await RPMRequestHandler().disconnect(
            request.type_device.value, PARTICIPANT, str(participant.id)
        )

        if response is None:
            logger.warning(f"[Correlation ID: {correlation_id}] No response from RPM disconnect service")
            raise HTTPException(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE,
                detail="Unable to disconnect from device service"
            )

        logger.info(
            f"[Correlation ID: {correlation_id}] Response from RPM disconnect service: {response}"
        )

        disconnected: bool = response.get("disconnected", False)

        if (
            disconnected
            and request.type_device == ParticipantActivityDevice.WITHINGS
        ):
            logger.info(f"[Correlation ID: {correlation_id}] Setting user weight status to False")
            await set_user_weight_status(participant, False, correlation_id)
            logger.info(f"[Correlation ID: {correlation_id}] User has been disconnected: {disconnected}")
            response["disconnected"] = DEVICE_DISCONNECTED
            return response

        logger.warning(
            f"[Correlation ID: {correlation_id}] Device not disconnected or not WITHINGS device. "
            f"Disconnected: {disconnected}, Device: {request.type_device}"
        )
        response["disconnected"] = DEVICE_CONNECTED
        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(
            f"[Correlation ID: {correlation_id}] Unexpected error in disconnect: {e}",
            exc_info=True
        )
        raise HTTPException(
            status_code=HTTPStatus.INTERNAL_SERVER_ERROR,
            detail=f"{ERROR_ON_DISCONNECTING}: {e}",
        ) from e
