from uuid import UUID

from strawberry.types import Info

from app import get_settings
from app.graphql_api.classes.types import UpdateBookingResponseType
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
    LiveSession,
    TopicEnum,
)
from ciba_participant.common.aws_handler import (
    SQSNotification,
    NotificationType,
    EmailNotificationEvent,
    send_to_sqs,
)

settings = get_settings()


async def create_booking(
    live_session_id: UUID, participant_id: UUID, status: BookingStatusEnum
) -> UpdateBookingResponseType:
    """
    Helper function to create a new booking
    """
    booking = await Booking.create(
        live_session_id=live_session_id,
        participant_id=participant_id,
        status=status,
    )

    # Send email
    sqs_notification = SQSNotification(
        type=NotificationType.SQS,
        email_event=EmailNotificationEvent.CLASS_BOOKED,
        data={
            "participant_id": participant_id,
            "live_session_id": live_session_id,
        },
    )
    send_to_sqs(
        queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
        message_body=sqs_notification.model_dump_json(),
    )

    return UpdateBookingResponseType(success=True, booking_id=booking.id)


async def handle_booked_status(
    booking: Booking, status: BookingStatusEnum
) -> UpdateBookingResponseType:
    """
    Helper function to handle the BOOKED status
    """
    if booking.status != BookingStatusEnum.CANCELED:
        return UpdateBookingResponseType(
            success=False, error="Booking was already made"
        )

    booking.status = status
    await booking.save()
    return UpdateBookingResponseType(success=True, booking_id=booking.id)


async def update_status_by_participant_id(
    participant_id: UUID,
    live_session_id: UUID,
    status: BookingStatusEnum,
) -> UpdateBookingResponseType:
    session = await LiveSession.get_or_none(id=live_session_id)

    if session is None:
        return UpdateBookingResponseType(
            success=False,
            error="Session not found",
        )

    await session.fetch_related("bookings", "webinar")

    participant_booking = await Booking.filter(
        live_session_id=live_session_id,
        participant_id=participant_id,
    ).first()

    if not participant_booking:
        if len(session.bookings) >= session.webinar.max_capacity:
            return UpdateBookingResponseType(
                success=False,
                error="Session is full",
            )

        if status == BookingStatusEnum.BOOKED:
            return await create_booking(
                live_session_id, participant_id, status
            )
        return UpdateBookingResponseType(
            success=False, error="Booking not found"
        )

    if status == BookingStatusEnum.BOOKED:
        response = await handle_booked_status(participant_booking, status)
        if response.success:
            # Send email
            sqs_notification = SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.CLASS_BOOKED,
                data={
                    "participant_id": participant_id,
                    "live_session_id": live_session_id,
                },
            )
            send_to_sqs(
                queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                message_body=sqs_notification.model_dump_json(),
            )
        return response

    if participant_booking.status in [
        BookingStatusEnum.ATTENDED,
    ]:
        return UpdateBookingResponseType(
            success=False,
            error=f"Live Session was already {participant_booking.status.value.lower()}",
        )

    if participant_booking.status != status:
        if status == BookingStatusEnum.CANCELED:
            # Send email
            sqs_notification = SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.CANCELLED_SESSION_BY_USER,
                data={
                    "participant_id": participant_id,
                    "live_session_id": live_session_id,
                },
            )
            send_to_sqs(
                queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                message_body=sqs_notification.model_dump_json(),
            )

        if session.webinar.topic == TopicEnum.INTRO_SESSION and status in [
            BookingStatusEnum.ATTENDED,
            BookingStatusEnum.WATCHED_RECORDING,
        ]:
            # Send email
            sqs_notification = SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.CLASSES_UNLOCKED,
                data={
                    "participant_id": participant_id,
                    "live_session_id": live_session_id,
                },
            )
            send_to_sqs(
                queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                message_body=sqs_notification.model_dump_json(),
            )

        participant_booking.status = status
        await participant_booking.save()

    return UpdateBookingResponseType(
        success=True, booking_id=participant_booking.id
    )


async def update_booking_status(
    info: Info,
    live_session_id: UUID,
    status: BookingStatusEnum,
) -> UpdateBookingResponseType:
    """
    Method to update the booking status
    """
    participant_id = info.context.user.sub
    return await update_status_by_participant_id(
        participant_id, live_session_id, status
    )
