# pylint: disable=duplicate-code
from datetime import datetime
from typing import List
from uuid import UUID

import pendulum
import strawberry
from strawberry.types import Info
from tortoise.queryset import QuerySet
from ciba_participant.cohort.models import (
    CohortMembers,
    CohortMembershipStatus,
)
from ciba_participant.participant.models import (
    Participant,
    SoleraParticipant,
    ParticipantStatus,
)
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ActivityUnit,
)
from ciba_participant.activity.crud import ParticipantActivityRepository

from app.auth.permissions import (
    IsAdmin,
    IsAuthenticated,
    IsCibaUserOrIsAuthenticated,
)
from app.graphql_api.pagination import Connection, Params, paginate
from app.graphql_api.participant.types import (
    ParticipantActivityType,
    ParticipantType,
    ParticipantWeightsType,
)
from app.graphql_api.participant.utils import parse_withings_connection_status
from app.log.logging import logger
from app.services.zendesk.zendesk import generate_zendesk_jwt_token


async def get_participant(participant_id: UUID) -> ParticipantType | None:
    """Get participant details."""
    participant = await Participant.filter(id=participant_id).get_or_none()
    # pylint: disable=no-member
    return await ParticipantType.marshal(participant) if participant else None


async def me(  # pylint: disable=invalid-name
    info: Info,
) -> ParticipantType | None:
    """
    Retrieve details of the logged-in participant.

    This asynchronous function retrieves the participant record using the user ID from the GraphQL context.
    It also fetches active solera data, selects the latest participant metadata (warning if multiple records are present),
    and obtains an active cohort membership (logging an error if not found). The returned ParticipantType includes personal
    details, solera identifiers, and cohort membership information, or None if the participant does not exist.
    """
    participant = await Participant.all_participants.filter(
        id=info.context.user.sub
    ).get_or_none()
    if not participant:
        return None
    solera_data = await SoleraParticipant.filter(
        participant_id=participant.id,
        status=ParticipantStatus.ACTIVE,
    ).first()

    meta_list = await participant.participant_meta
    if len(meta_list) > 1:
        logger.warning(
            "Multiple metadata records found for participant %s",
            participant.id,
        )

    latest_meta = meta_list[-1] if meta_list else None
    is_weight = await parse_withings_connection_status(participant.id)
    disenrollment_reason = (
        latest_meta.metadata.get("disenrolledReason") if latest_meta else None
    )
    disenrollment_date_raw = (
        latest_meta.metadata.get("disenrollmentDate") if latest_meta else None
    )
    disenrollment_date = (
        pendulum.parse(disenrollment_date_raw)
        if disenrollment_date_raw
        else None
    )

    cohort_membership = await CohortMembers.filter(
        participant_id=participant.id,
        status=CohortMembershipStatus.ACTIVE,
    ).get_or_none()

    cohort = None
    if cohort_membership is None:
        logger.error(
            "Active cohort membership not found for participant %s",
            participant.id,
        )
        cohort_id = None
    else:
        cohort = await cohort_membership.cohort
        cohort_id = cohort.id

    return ParticipantType(
        id=participant.id,
        email=participant.email,
        first_name=participant.first_name,
        last_name=participant.last_name,
        chat_identity=participant.chat_identity,
        group_id=participant.group_id,
        member_id=participant.member_id,
        status=participant.status.value,
        cohort_end_date=await cohort.end_date if cohort else None,
        cognito_sub=participant.cognito_sub,
        medical_record=participant.medical_record,
        heads_up_token=None,
        solera_id=(solera_data.solera_id if solera_data else None),
        solera_program_id=(
            solera_data.solera_program_id if solera_data else None
        ),
        is_weight=is_weight,
        program_group_id=cohort_id,
        disenrollment_reason=disenrollment_reason,
        disenrollment_date=disenrollment_date,
    )


@strawberry.input
class ParticipantsFilterType:
    """GraphQl participants filter input."""

    ids: list[UUID] | None = None
    emails: list[str] | None = None
    group_id: UUID | None = None

    def filter(self, queryset: QuerySet) -> QuerySet:
        """Filter participants."""
        if self.ids:
            queryset = queryset.filter(id__in=self.ids)
        if self.emails:
            queryset = queryset.filter(email__in=self.emails)
        if self.group_id:
            queryset = queryset.filter(cohort__id=self.group_id)
        return queryset


async def get_participants(
    page: int = 1,
    per_page: int = 10,
    filters: ParticipantsFilterType | None = None,
) -> Connection[ParticipantType]:
    """Get participant stub."""
    queryset = Participant.all()
    if filters:
        queryset = filters.filter(queryset)
    return await paginate(
        queryset, params=Params(page=page, per_page=per_page)
    )


async def get_participant_activity(
    info: Info,
) -> List[ParticipantActivityType] | None:
    """Get user activity."""

    participant = await Participant.filter(
        id=info.context.user.sub
    ).get_or_none()
    data = (
        await ParticipantActivityRepository.get_activities_by_participant_id(
            participant_id=participant.id
        )
    )

    return [
        ParticipantActivityType(
            id=participant_activity.id,
            participant_id=participant_activity.participant_id,
            value=participant_activity.value,
            activity_type=participant_activity.activity_type.value,
            unit=participant_activity.unit.value,
            activity_device=participant_activity.activity_device.value,
            created_at=participant_activity.created_at,
        )
        for participant_activity in data
    ]


async def get_participant_weights(
    info: Info, start_date: str = None, end_date: str = None
) -> List[ParticipantWeightsType] | None:
    """Get user weight activity."""

    start_date = pendulum.parse(start_date) if start_date else start_date
    end_date = pendulum.parse(end_date) if end_date else start_date

    participant = await Participant.filter(
        id=info.context.user.sub
    ).get_or_none()

    data = (
        await ParticipantActivityRepository.get_activities_by_participant_id(
            participant_id=participant.id,
            activity_type=ParticipantActivityEnum.WEIGHT.value,
            start_date=start_date,
            end_date=end_date,
        )
    )

    logger.debug("withings_data: %s", len(data))

    result = []
    for el in data:
        value = el.value
        value = float(value) if value else 0.0
        created_at: datetime = (
            pendulum.parse(el.created_at)
            if isinstance(el.created_at, str)
            else el.created_at
        )

        result.append(
            ParticipantWeightsType(
                id=el.id,
                participant_id=el.participant_id,
                value=value,
                activity_type=ParticipantActivityEnum.WEIGHT.value,
                unit=ActivityUnit.LB.value,
                activity_device=el.activity_device.value,
                created_at=created_at.strftime("%Y-%m-%d %H:%M:%S"),
            )
        )

    return result


async def get_participant_zendesk_jwt_token(info: Info) -> str:
    """Get Zendesk JWT token for the logged-in user."""
    participant = await Participant.filter(
        id=info.context.user.sub
    ).get_or_none()

    if not participant:
        raise ValueError("Participant not found")
    return generate_zendesk_jwt_token(participant)


@strawberry.type()
class ParticipantQuery:
    """Participant graphql queries."""

    get_participant = strawberry.field(
        resolver=get_participant, permission_classes=[IsAdmin]
    )
    get_participants = strawberry.field(
        resolver=get_participants,
        permission_classes=[IsCibaUserOrIsAuthenticated],
    )
    me = strawberry.field(resolver=me, permission_classes=[IsAuthenticated])

    get_participant_activity = strawberry.field(
        resolver=get_participant_activity, permission_classes=[IsAuthenticated]
    )
    get_participant_weights = strawberry.field(
        resolver=get_participant_weights, permission_classes=[IsAuthenticated]
    )
    get_participant_zendesk_jwt_token = strawberry.field(
        resolver=get_participant_zendesk_jwt_token,
        permission_classes=[IsAuthenticated],
    )
